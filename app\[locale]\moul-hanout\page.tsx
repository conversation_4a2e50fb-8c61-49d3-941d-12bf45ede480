import { useTranslations } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';
import { Metadata } from 'next';
import MoulHanoutPage from '@/components/MoulHanoutPage';

type Props = {
  params: { locale: string };
};

export function generateMetadata({ params: { locale } }: Props): Metadata {
  const title = locale === 'ar'
    ? 'حانوت عام - دبر بقالتك بسهولة'
    : 'Moul Hanout CRM – Gérez votre épicerie facilement';

  const description = locale === 'ar'
    ? 'نظام إدارة مصمم خصيصا للبقالات والمحلات العامة والمتاجر التقليدية المغربية.'
    : 'Un système de gestion spécialement conçu pour les épiceries, magasins généraux et commerces traditionnels marocains.';

  return {
    title,
    description,
    keywords: locale === 'ar'
      ? 'حانوت, بقالة, إدارة مبيعات, زبائن, مخزون, المغرب'
      : 'épicerie, magasin général, gestion ventes, clients, stock, Maroc',
    openGraph: {
      title,
      description,
      type: 'website',
      locale: locale === 'ar' ? 'ar_MA' : 'fr_MA',
    },
  };
}

export default function Page({ params: { locale } }: Props) {
  setRequestLocale(locale);
  return <MoulHanoutPage />;
}

export function generateStaticParams() {
  return [{ locale: 'fr' }, { locale: 'ar' }];
}
