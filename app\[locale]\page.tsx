import { useTranslations } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';
import { Metadata } from 'next';
import HomePage from '@/components/HomePage';

type Props = {
  params: { locale: string };
};

export function generateMetadata({ params: { locale } }: Props): Metadata {
  // This would be better with actual translations, but for now we'll use static values
  const title = locale === 'ar'
    ? 'CRM الحانوت - نظام إدارة للمحلات التجارية المغربية'
    : 'CRM الحانوت - Système de gestion pour commerces marocains';

  const description = locale === 'ar'
    ? 'نظام إدارة مجاني وبسيط للمحلات التجارية المغربية. دبر زبائنك، تتبع مبيعاتك وحسن تجارتك.'
    : 'Système de gestion gratuit et simple pour les commerces marocains. G<PERSON><PERSON> vos clients, suivez vos ventes et optimisez votre business.';

  return {
    title,
    description,
    keywords: locale === 'ar'
      ? 'CRM, إدارة تجارة, المغرب, بقالة, إلكترونيات, مبيعات, زبائن'
      : 'CRM, gestion commerce, Maroc, épicerie, électronique, ventes, clients',
    openGraph: {
      title,
      description,
      type: 'website',
      locale: locale === 'ar' ? 'ar_MA' : 'fr_MA',
    },
    alternates: {
      languages: {
        'fr': '/fr',
        'ar': '/ar',
      },
    },
  };
}

export default function Page({ params: { locale } }: Props) {
  // Enable static rendering
  setRequestLocale(locale);

  return <HomePage />;
}

export function generateStaticParams() {
  return [{ locale: 'fr' }, { locale: 'ar' }];
}
