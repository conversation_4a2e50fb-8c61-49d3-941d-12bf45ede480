import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import createIntlMiddleware from 'next-intl/middleware';

// Create the intl middleware
const intlMiddleware = createIntlMiddleware({
  locales: ['fr', 'ar'],
  defaultLocale: 'fr'
});

// Ensure JWT_SECRET is properly configured
const jwtSecret = process.env.JWT_SECRET;
if (!jwtSecret) {
  console.error('❌ JWT_SECRET is not set in environment variables');
  throw new Error('JWT_SECRET is required. Please set JWT_SECRET in your .env.local file.');
}
if (jwtSecret.length < 32) {
  console.error(`❌ JWT_SECRET is too short: ${jwtSecret.length} characters (minimum 32 required)`);
  throw new Error(`JWT_SECRET must be at least 32 characters long. Current length: ${jwtSecret.length}`);
}
console.log(`✅ JWT_SECRET loaded successfully (${jwtSecret.length} characters)`);
const JWT_SECRET = new TextEncoder().encode(jwtSecret);

// Protected routes that require authentication (without locale prefix)
const protectedRoutes = [
  '/dashboard',
  '/electronics',
  '/products',
  '/customers',
  '/sales',
  '/purchases',
  '/returns',
  '/credit',
  '/analytics'
];

// Helper function to check if a path is protected
function isProtectedPath(pathname: string): boolean {
  // Remove locale prefix to check the actual route
  const pathWithoutLocale = pathname.replace(/^\/(fr|ar)/, '') || '/';
  return protectedRoutes.some(route => pathWithoutLocale.startsWith(route));
}

// Note: Business model route validation is handled by RouteProtection component

async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

// Note: Business model validation is handled by RouteProtection component
// This middleware focuses on authentication and basic route protection

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for API routes, static files, and favicon
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // Handle internationalization first
  const intlResponse = intlMiddleware(request);

  // If intl middleware returns a redirect, return it
  if (intlResponse.status === 302 || intlResponse.status === 307) {
    return intlResponse;
  }

  // Get token from cookies or Authorization header
  const token = request.cookies.get('auth-token')?.value ||
    request.headers.get('Authorization')?.replace('Bearer ', '');

  // Check if route requires authentication
  const isProtectedRoute = isProtectedPath(pathname);

  // If accessing a protected route without token, redirect to login
  if (isProtectedRoute && !token) {
    // Extract locale from pathname
    const locale = pathname.match(/^\/(fr|ar)/)?.[1] || 'fr';
    const loginUrl = new URL(`/${locale}/login`, request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If user has token, verify it
  if (token) {
    const payload = await verifyToken(token);

    if (!payload) {
      // Invalid token, redirect to login
      const locale = pathname.match(/^\/(fr|ar)/)?.[1] || 'fr';
      const response = NextResponse.redirect(new URL(`/${locale}/login`, request.url));
      response.cookies.delete('auth-token');
      return response;
    }

    // Token is valid - add user info to request headers for API routes
    if (isProtectedRoute) {
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-user-id', payload.id as string);
      requestHeaders.set('x-user-email', payload.email as string);

      const response = NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });

      return response;
    }
  }

  // Return the intl response for all other cases
  return intlResponse;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
